"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/tutorials/tutorialMapping.js":
/*!******************************************!*\
  !*** ./src/tutorials/tutorialMapping.js ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getTutorialForRoute: () => (/* binding */ getTutorialForRoute)\n/* harmony export */ });\n/**\r\n * Este arquivo mapeia tutoriais específicos para diferentes rotas da aplicação\r\n * Cada rota principal tem seu próprio tutorial com passos específicos\r\n */ // Tutorial para o Dashboard principal\nconst dashboardTutorial = [\n    {\n        title: \"Bem-vindo ao Dashboard\",\n        content: \"Este é o seu painel central, onde você pode visualizar informações importantes e acessar todos os módulos do sistema.\",\n        selector: \"#Main-Container-Dashboard\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Cartão de Boas-vindas\",\n        content: \"Aqui você visualiza informações personalizadas conforme o horário do dia e seu perfil de usuário.\",\n        selector: \".bg-gradient-to-r.from-white.to-gray-50\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Módulos Disponíveis\",\n        content: \"Cada card representa um módulo do sistema. Clique em um deles para acessar suas funcionalidades específicas.\",\n        selector: \".grid.grid-cols-1.md\\\\:grid-cols-2.lg\\\\:grid-cols-3.xl\\\\:grid-cols-5 > div:first-child\",\n        position: \"right\"\n    },\n    {\n        title: \"Próximos Agendamentos\",\n        content: \"Visualize seus próximos agendamentos com informações detalhadas sobre pacientes, profissionais e horários.\",\n        selector: \".bg-white.dark\\\\:bg-gray-800.rounded-xl h3:has(.calendar)\",\n        position: \"top\"\n    },\n    {\n        title: \"Ações Rápidas\",\n        content: \"Acesse diretamente as funcionalidades mais utilizadas sem precisar navegar pelos diferentes módulos.\",\n        selector: \"div:has(> h3:has(.activity))\",\n        position: \"left\",\n        highlightPadding: 15\n    }\n];\n//----------------------------------------------AGENDAMENTO--------------------------------------------------------------------------------------\n// Tutorial para Calendário de Agendamentos\nconst calendarTutorial = [\n    {\n        title: \"Calendário de Agendamentos\",\n        content: \"Aqui você pode visualizar, criar e gerenciar todos os seus compromissos.\",\n        selector: \".fc-view-harness\",\n        position: \"top\"\n    },\n    {\n        title: \"Filtros\",\n        content: \"Use estes filtros para encontrar agendamentos específicos por profissional, paciente, tipo de serviço ou local.\",\n        selector: \".filter-section\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Criar Agendamento\",\n        content: \"Para criar um novo agendamento, basta clicar em qualquer espaço vazio no calendário.\",\n        selector: \".fc-daygrid-day:not(.fc-day-other):nth-child(3)\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Visualizações\",\n        content: \"Alterne entre visualizações de mês, semana ou dia para ver seus agendamentos com diferentes níveis de detalhe.\",\n        selector: \".fc-toolbar-chunk:last-child\",\n        position: \"bottom\"\n    }\n];\n// Tutorial para página de horários de trabalho\nconst workingHoursTutorial = [\n    {\n        title: \"Horários de Trabalho\",\n        content: \"Gerencie os Horários de Trabalho dos funcionários cadastrados no sistema.\",\n        selector: \"table\",\n        position: \"top\"\n    },\n    {\n        title: \"Exportar Dados\",\n        content: \"Exporte os horários de trabalho em diferentes formatos (Excel ou PDF) usando este botão.\",\n        selector: \".export-button\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Horários de Trabalho pelo dia da Semana\",\n        content: \"Clique aqui para mudar os dias a serem preenchidos.\",\n        selector: \"#bordaDiaDaSemana\",\n        position: \"left\"\n    },\n    {\n        title: \"Horários selecionáveis\",\n        content: \"Clique aqui para selecionar os horários daquele dia da semana que o profissional está disponível.\",\n        selector: \"#bordaSelecaoHorario\",\n        position: \"left\"\n    },\n    {\n        title: \"Horários por Funcionário\",\n        content: \"Clique aqui para selecionar os horários da semana completa de um único funcionário.\",\n        selector: \"#bordaNomeFuncionario\",\n        position: \"left\"\n    },\n    {\n        title: \"Salve seus Horários e Utilize os Filtros\",\n        content: \"Clique aqui para salvar os horários, e para visualizar utilize os filtros.\",\n        selector: \"#bordaSalvarHorarioTrabalho\",\n        position: \"right\"\n    }\n];\n// Tutorial para página de localizações\nconst locationsTutorial = [\n    {\n        title: \"Localizações\",\n        content: \"Gerencie os locais de atendimento disponíveis no sistema.\",\n        selector: \"table\",\n        position: \"top\"\n    },\n    {\n        title: \"Exportar Dados\",\n        content: \"Exporte a lista de localizações em diferentes formatos (Excel ou PDF) usando este botão.\",\n        selector: \".export-button\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Adicionar Localização\",\n        content: \"Clique aqui para cadastrar um novo local de atendimento.\",\n        selector: \".add-button, button[title*='Nova'], button[title*='Novo']\",\n        position: \"left\"\n    }\n];\n// Tutorial para página de ocupações\nconst ocuppancyTutorial = [\n    {\n        title: \"Análise de Ocupação\",\n        content: \"Visualize a taxa de Ocupação de locais, profissionais e até pelo tipo do serviço caso deseje limitar.\",\n        selector: \"#bordaTaxaOcupacao\",\n        position: \"top\"\n    },\n    {\n        title: \"Exportar Dados\",\n        content: \"Exporte os dados de ocupação em diferentes formatos (Excel ou PDF) usando este botão.\",\n        selector: \".export-button\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Use os filtros!\",\n        content: \"Clique para selecionar o periodo do tempo, a data referência, os profissionais, serviços ou até os locais.\",\n        selector: \"#bordaFiltroOcupacao\",\n        position: \"left\"\n    }\n];\n// Tutorial para tipos de serviço\nconst serviceTypesTutorial = [\n    {\n        title: \"Tipos de Serviço\",\n        content: \"Gerencie os serviços oferecidos pela sua clínica ou consultório.\",\n        selector: \"table\",\n        position: \"top\"\n    },\n    {\n        title: \"Exportar Dados\",\n        content: \"Exporte a lista de tipos de serviço em diferentes formatos (Excel ou PDF) usando este botão.\",\n        selector: \".export-button\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Adicionar Tipo de Serviço\",\n        content: \"Clique aqui para cadastrar um novo tipo de serviço.\",\n        selector: \".add-button, button[title*='Novo']\",\n        position: \"left\"\n    }\n];\n// Tutorial para relatório de agendamentos\nconst appointmentsReportTutorial = [\n    {\n        title: \"Relatório de Agendamentos\",\n        content: \"Esta tela permite visualizar e gerenciar todos os agendamentos do sistema com filtros avançados e opções de exportação.\",\n        selector: \"h1\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Filtros de Agendamentos\",\n        content: \"Use estes filtros para encontrar agendamentos específicos por data, status, profissional, paciente, local ou tipo de serviço.\",\n        selector: \"form\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Estatísticas\",\n        content: \"Visualize estatísticas rápidas sobre os agendamentos filtrados, incluindo totais por status.\",\n        selector: \".grid.grid-cols-1.md\\\\:grid-cols-4\",\n        position: \"top\"\n    },\n    {\n        title: \"Tabela de Agendamentos\",\n        content: \"Veja todos os agendamentos em formato de tabela, com opções para editar, excluir e alterar o status.\",\n        selector: \"table\",\n        position: \"top\"\n    },\n    {\n        title: \"Ações de Agendamento\",\n        content: \"Use estes botões para editar, excluir ou alterar o status de um agendamento.\",\n        selector: \".flex.space-x-2\",\n        position: \"left\"\n    },\n    {\n        title: \"Exportar Dados\",\n        content: \"Exporte os agendamentos filtrados em diferentes formatos para análise externa.\",\n        selector: \".export-button\",\n        position: \"left\"\n    }\n];\n//----------------------------------------------PESSOAS--------------------------------------------------------------------------------------\n// Tutorial para página de Clientes\nconst clientsListTutorial = [\n    {\n        title: \"Lista de Clientes\",\n        content: \"Aqui você pode visualizar, filtrar e gerenciar todos os clientes cadastrados no sistema.\",\n        selector: \"table\",\n        position: \"top\"\n    },\n    {\n        title: \"Pesquisa e Filtros\",\n        content: \"Use estes campos para buscar clientes por nome ou email, e filtrar por status.\",\n        selector: \"form\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Adicionar Cliente\",\n        content: \"Clique aqui para cadastrar um novo cliente no sistema.\",\n        selector: \".add-button, button[title*='Novo']\",\n        position: \"left\"\n    }\n];\n// Tutorial para página de Pacientes\nconst patientsListTutorial = [\n    {\n        title: \"Lista de Pacientes\",\n        content: \"Aqui você pode visualizar, filtrar e gerenciar todos os pacientes cadastrados no sistema.\",\n        selector: \"table\",\n        position: \"top\"\n    },\n    {\n        title: \"Pesquisa e Filtros\",\n        content: \"Use estes campos para buscar pacientes por nome, email ou CPF, e filtrar por status.\",\n        selector: \"form\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Adicionar Paciente\",\n        content: \"Clique aqui para cadastrar um novo paciente no sistema.\",\n        selector: \".add-button, button[title*='Novo']\",\n        position: \"left\"\n    },\n    {\n        title: \"Ações por Paciente\",\n        content: \"Nesta coluna você pode visualizar detalhes, editar, ativar/desativar ou excluir um paciente.\",\n        selector: \"td:last-child\",\n        position: \"left\"\n    }\n];\n// Tutorial para página de detalhes do paciente\nconst patientDetailsTutorial = [\n    {\n        title: \"Detalhes do Paciente\",\n        content: \"Esta página mostra informações detalhadas sobre o paciente selecionado.\",\n        selector: \"h1\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Informações Pessoais\",\n        content: \"Aqui você encontra os dados pessoais e de contato do paciente.\",\n        selector: \"h2\",\n        position: \"right\"\n    },\n    {\n        title: \"Documentos\",\n        content: \"Nesta seção você pode visualizar e gerenciar documentos relacionados ao paciente.\",\n        selector: \"h3\",\n        position: \"bottom\"\n    }\n];\n// Tutorial para página de convênios\nconst insurancesTutorial = [\n    {\n        title: \"Convênios\",\n        content: \"Gerencie os convênios disponíveis para seus pacientes nesta página.\",\n        selector: \"table\",\n        position: \"top\"\n    },\n    {\n        title: \"Adicionar Convênio\",\n        content: \"Clique aqui para cadastrar um novo convênio no sistema.\",\n        selector: \"button:has(.lucide-plus)\",\n        position: \"left\"\n    }\n];\n// Tutorial para página de Usuários\nconst admUsersTutorial = [\n    {\n        title: \"Gerenciamento de Usuários\",\n        content: \"Esta página permite gerenciar todos os usuários do sistema. Aqui você pode criar, editar, ativar/desativar e excluir usuários, além de gerenciar suas permissões e acesso aos módulos.\",\n        selector: \".ModuleHeader h1\",\n        position: \"bottom\",\n        dialogOffsetY: 10\n    },\n    {\n        title: \"Exportar Dados\",\n        content: \"Exporte a lista de usuários em diferentes formatos (Excel ou PDF) usando este botão.\",\n        selector: \".export-button\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Adicionar Novo Usuário\",\n        content: \"Clique aqui para criar um novo usuário no sistema. Você poderá definir informações pessoais, credenciais de acesso, profissão, função e permissões.\",\n        selector: \"button:has(.lucide-plus)\",\n        position: \"left\",\n        dialogOffsetX: 15,\n        highlightPadding: 5\n    },\n    {\n        title: \"Pesquisa e Filtros\",\n        content: \"Use estes campos para buscar usuários por nome, email ou profissão. Você também pode filtrar por status (ativo/inativo) e por função (administrador, funcionário, etc).\",\n        selector: \"#filtroUsuario\",\n        position: \"bottom\",\n        dialogOffsetY: 10\n    },\n    {\n        title: \"Tabela de Usuários\",\n        content: \"Esta tabela exibe todos os usuários do sistema com suas informações principais. Você pode ordenar por qualquer coluna clicando no cabeçalho.\",\n        selector: \"#admin-users-table\",\n        position: \"top\",\n        dialogOffsetY: 10\n    },\n    {\n        title: \"Ações de Usuário\",\n        content: \"Aqui você encontra botões para editar informações, gerenciar módulos, permissões, função, ativar/desativar ou excluir usuários.\",\n        selector: \"td:last-child .flex.justify-end\",\n        position: \"left\",\n        dialogOffsetX: 15\n    },\n    {\n        title: \"Editar Usuário\",\n        content: \"Clique neste botão para modificar as informações pessoais e credenciais do usuário selecionado.\",\n        selector: \"#edicaoUsuario\",\n        position: \"left\",\n        dialogOffsetX: 15\n    },\n    {\n        title: \"Gerenciar Módulos\",\n        content: \"Este botão permite definir a quais módulos do sistema o usuário terá acesso. Administradores de sistema e de empresa têm acesso automático a todos os módulos.\",\n        selector: \"#gerenciarModulo\",\n        position: \"left\",\n        dialogOffsetX: 15\n    },\n    {\n        title: \"Gerenciar Permissões\",\n        content: \"Controle detalhado das permissões do usuário dentro de cada módulo. Você pode definir o que o usuário pode visualizar, criar, editar ou excluir.\",\n        selector: \"#gerenciarPermissoes\",\n        position: \"left\",\n        dialogOffsetX: 15\n    },\n    {\n        title: \"Fluxo de Criação de Usuário\",\n        content: \"O fluxo recomendado para criar um usuário é: 1) Informações pessoais, 2) Documentos, 3) Definir função, 4) Atribuir módulos (para funcionários), 5) Configurar permissões específicas.\",\n        selector: \"#admin-users-table\",\n        position: \"bottom\",\n        dialogOffsetY: 10\n    }\n];\n//----------------------------------------------ADMINISTRAÇÃO--------------------------------------------------------------------------------------\n// Tutorial para página de Profissões/Grupos\nconst professionsGroupsTutorial = [\n    {\n        title: \"Gerenciamento de Profissões e Grupos\",\n        content: \"Esta página permite gerenciar as profissões e grupos de profissões disponíveis no sistema. Estas informações são essenciais para a criação de usuários e definição de permissões.\",\n        selector: \".text-2xl.font-bold\",\n        position: \"bottom\",\n        dialogOffsetY: 10\n    },\n    {\n        title: \"Abas de Navegação\",\n        content: \"Alterne entre as abas 'Profissões' e 'Grupos de Profissões' para gerenciar cada tipo de cadastro separadamente.\",\n        selector: \"button[role='tab']\",\n        position: \"bottom\",\n        dialogOffsetY: 15\n    },\n    {\n        title: \"Exportar Dados\",\n        content: \"Exporte a lista de profissões ou grupos em diferentes formatos (Excel ou PDF) usando este botão.\",\n        selector: \".export-button\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Adicionar Nova Profissão/Grupo\",\n        content: \"Clique neste botão para criar uma nova profissão ou grupo no sistema. Você poderá definir nome, descrição, associações e status.\",\n        selector: \"button:has(.lucide-plus)\",\n        position: \"left\",\n        highlightPadding: 5,\n        dialogOffsetX: 15\n    },\n    {\n        title: \"Filtros de Busca\",\n        content: \"Use estes campos para buscar profissões ou grupos por nome, filtrar por status ou outras propriedades.\",\n        selector: \"input[type='text']\",\n        position: \"bottom\",\n        dialogOffsetY: 10\n    },\n    {\n        title: \"Tabela de Profissões/Grupos\",\n        content: \"Esta tabela exibe todas as profissões ou grupos cadastrados com suas informações principais. Você pode ordenar por qualquer coluna clicando no cabeçalho.\",\n        selector: \"table\",\n        position: \"top\",\n        dialogOffsetY: 10\n    },\n    {\n        title: \"Ações Disponíveis\",\n        content: \"Aqui você encontra botões para ver usuários associados, editar ou excluir profissões e grupos.\",\n        selector: \"td:last-child .flex.justify-end\",\n        position: \"left\",\n        dialogOffsetX: 15\n    },\n    {\n        title: \"Visibilidade de Empresa\",\n        content: \"Administradores de sistema podem ver a qual empresa cada profissão ou grupo pertence. Administradores de empresa só veem registros da própria empresa.\",\n        selector: \"th:nth-child(3)\",\n        position: \"bottom\",\n        dialogOffsetY: 10\n    },\n    {\n        title: \"Dica Importante\",\n        content: \"Configure primeiro os grupos de profissões antes de criar as profissões individuais. Isso facilitará a organização e atribuição de permissões posteriormente.\",\n        selector: \"table\",\n        position: \"bottom\",\n        dialogOffsetY: 10\n    }\n];\n// Tutorial para página de Configurações\nconst settingsTutorial = [\n    {\n        title: \"Configurações do Sistema\",\n        content: \"Esta página permite personalizar diversos aspectos do sistema de acordo com as necessidades da sua empresa ou organização.\",\n        selector: \"h1, .text-2xl.font-bold\",\n        position: \"bottom\",\n        dialogOffsetY: 10\n    },\n    {\n        title: \"Navegação por Abas\",\n        content: \"Use estas abas para navegar entre diferentes categorias de configurações. As opções disponíveis dependem do seu nível de acesso no sistema.\",\n        selector: \"button.flex.items-center.gap-2\",\n        position: \"bottom\",\n        dialogOffsetY: 15\n    },\n    {\n        title: \"Configurações Gerais\",\n        content: \"Defina informações básicas como nome do site, URL, email administrativo, formato de data e hora, e outras configurações globais.\",\n        selector: \"button:has(.lucide-cog), button:has(.lucide-settings)\",\n        position: \"bottom\",\n        dialogOffsetY: 10\n    },\n    {\n        title: \"Configurações de Empresa\",\n        content: \"Gerencie informações da empresa como nome, CNPJ, endereço, contatos e configurações específicas. Administradores de sistema podem gerenciar múltiplas empresas.\",\n        selector: \"button:has(.lucide-building)\",\n        position: \"left\",\n        dialogOffsetX: 15\n    },\n    {\n        title: \"Configurações de Unidades\",\n        content: \"Administre as unidades ou filiais da empresa, incluindo endereços, horários de funcionamento e configurações específicas de cada local.\",\n        selector: \"button:has(.lucide-map-pin)\",\n        position: \"left\",\n        dialogOffsetX: 15\n    },\n    {\n        title: \"Configurações de Email\",\n        content: \"Configure os servidores de email, modelos de mensagens e notificações automáticas enviadas pelo sistema.\",\n        selector: \"button:has(.lucide-mail)\",\n        position: \"left\",\n        dialogOffsetX: 15\n    },\n    {\n        title: \"Configurações de Backup\",\n        content: \"Configure as opções de backup automático do sistema, incluindo frequência, horário e tipos de arquivos a serem salvos.\",\n        selector: \"button:has(.lucide-database), button:has(.lucide-hard-drive)\",\n        position: \"left\",\n        dialogOffsetX: 15\n    },\n    {\n        title: \"Configurações de Segurança\",\n        content: \"Defina políticas de senha, autenticação de dois fatores e outras configurações de segurança para proteger o sistema.\",\n        selector: \"button:has(.lucide-shield), button:has(.lucide-lock)\",\n        position: \"left\",\n        dialogOffsetX: 15\n    },\n    {\n        title: \"Formulários de Configuração\",\n        content: \"Preencha os formulários com as informações necessárias para configurar o sistema de acordo com as necessidades da sua empresa.\",\n        selector: \"input[type='text'], select\",\n        position: \"bottom\",\n        dialogOffsetY: 10\n    },\n    {\n        title: \"Opções de Localização\",\n        content: \"Configure o fuso horário, formato de data e hora para todo o sistema de acordo com as necessidades da sua região.\",\n        selector: \"select\",\n        position: \"bottom\",\n        dialogOffsetY: 10\n    },\n    {\n        title: \"Salvar Alterações\",\n        content: \"Lembre-se de clicar em 'Salvar' após fazer modificações em cada seção para que as alterações sejam aplicadas.\",\n        selector: \"button.bg-primary-500, button.bg-orange-500, button[type='submit']\",\n        position: \"left\",\n        dialogOffsetX: 15\n    }\n];\n//----------------------------------------------DASHBOARDS NO GERAL--------------------------------------------------------------------------------------\n// Tutorial para o Dashboard de Administração\nconst adminDashboardTutorial = [\n    {\n        title: \"Dashboard de Administração\",\n        content: \"Este painel oferece uma visão geral do sistema com métricas importantes, estatísticas de uso e informações sobre usuários e atividades recentes.\",\n        selector: \"h1\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Exportar Dados\",\n        content: \"Exporte os dados do dashboard em diferentes formatos (Excel ou PDF) usando este botão.\",\n        selector: \".export-button\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Seletor de Empresa\",\n        content: \"Administradores de sistema podem selecionar diferentes empresas para visualizar dados específicos de cada uma. Administradores de empresa veem apenas dados da própria empresa.\",\n        selector: \"select\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Cartões de Estatísticas\",\n        content: \"Estes cartões mostram métricas importantes como total de usuários, usuários ativos, clientes e agendamentos, com indicadores de crescimento.\",\n        selector: \".grid\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Gráficos e Visualizações\",\n        content: \"Visualize dados importantes do sistema através de gráficos interativos que mostram tendências e distribuições.\",\n        selector: \".lg\\\\:col-span-2\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Seletor de Período\",\n        content: \"Filtre os dados do gráfico por diferentes períodos de tempo para análises mais específicas.\",\n        selector: \"select:nth-of-type(2)\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Distribuição de Usuários\",\n        content: \"Este gráfico mostra a distribuição de usuários por módulo, ajudando a identificar quais áreas do sistema são mais utilizadas.\",\n        selector: \".h-80\",\n        position: \"left\"\n    },\n    {\n        title: \"Tabelas de Informações\",\n        content: \"Visualize informações detalhadas sobre usuários ativos e atividades recentes no sistema.\",\n        selector: \"table\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Informações do Sistema\",\n        content: \"Visualize detalhes técnicos como versão do sistema, status do servidor, uso de recursos e outras informações relevantes para administradores.\",\n        selector: \".grid:last-child\",\n        position: \"bottom\"\n    }\n];\n// Tutorial para todos os tipos de Dashboards\nconst dashboardsTutorial = [\n    {\n        title: \"Dashboard\",\n        content: \"Use filtros no seu Dashboard ou busque para visualizar as informações que deseja\",\n        selector: \"#bordadashboards\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Tipo de Dashboard\",\n        content: \"Cada Dashboard tem suas informações retiradas dependendo de seu módulo.\",\n        selector: \"#gradedashboards\",\n        position: \"top\"\n    }\n];\n// Mapa de rotas para tutoriais\n// Cada chave é um padrão de URL, e o valor é o tutorial correspondente\nconst tutorialMap = {\n    '/dashboard': dashboardTutorial,\n    '/dashboard/admin/users': admUsersTutorial,\n    '/dashboard/admin/dashboard': adminDashboardTutorial,\n    '/dashboard/admin/professions': professionsGroupsTutorial,\n    '/dashboard/admin/settings': settingsTutorial,\n    '/dashboard/scheduler/appointments-dashboard': dashboardsTutorial,\n    '/dashboard/scheduler/working-hours': workingHoursTutorial,\n    '/dashboard/scheduler/occupancy': ocuppancyTutorial,\n    '/dashboard/scheduler/calendar': calendarTutorial,\n    '/dashboard/scheduler/appointments-report': appointmentsReportTutorial,\n    '/dashboard/people/persons': patientsListTutorial,\n    '/dashboard/people/clients': clientsListTutorial,\n    '/dashboard/people/persons/[id]': patientDetailsTutorial,\n    '/dashboard/people/insurances': insurancesTutorial,\n    '/dashboard/scheduler/locations': locationsTutorial,\n    '/dashboard/scheduler/service-types': serviceTypesTutorial\n};\n/**\r\n   * Função para obter o tutorial apropriado com base na rota atual\r\n   * @param {string} pathname - Caminho da URL atual\r\n   * @returns {Object} Tutorial correspondente ou null se não houver correspondência\r\n   */ const getTutorialForRoute = (pathname)=>{\n    // Tenta encontrar uma correspondência exata primeiro\n    if (tutorialMap[pathname]) {\n        return {\n            steps: tutorialMap[pathname],\n            name: pathname.replace(/\\//g, '-').replace(/^-/, '') // Converte '/dashboard' para 'dashboard'\n        };\n    }\n    // Verifica rotas dinâmicas com parâmetros (ex: /dashboard/people/persons/123)\n    for(const route in tutorialMap){\n        if (route.includes('[id]') && pathname.match(new RegExp(route.replace('[id]', '\\\\d+')))) {\n            return {\n                steps: tutorialMap[route],\n                name: route.replace(/\\//g, '-').replace(/^-/, '').replace('[id]', 'details')\n            };\n        }\n    }\n    // Se não encontrou nenhuma correspondência\n    return null;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (tutorialMap);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/tutorials/tutorialMapping.js\n"));

/***/ })

});