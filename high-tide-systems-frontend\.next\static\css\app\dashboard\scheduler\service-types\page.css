/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/scrollbar.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/* Estilos de scrollbar personalizados para diferentes módulos */

/* Estilo base para scrollbar */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
  background-color: rgba(156, 163, 175, 0.5); /* Cor padrão - cinza */
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Dark mode base */
.dark::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.dark::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}

/* Aplicar ao body - com maior especificidade */
html body::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

html body::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 0;
}

html body::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 3px solid transparent;
  background-clip: content-box;
  background-color: rgba(156, 163, 175, 0.5);
}

html body::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Dark mode para body - com maior especificidade */
html.dark body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

html.dark body::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

html.dark body::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}

/* Módulo People (laranja) */
.module-people::-webkit-scrollbar-thumb {
  background-color: rgba(249, 115, 22, 0.4); /* orange-500 com opacidade */
}

.module-people::-webkit-scrollbar-thumb:hover {
  background-color: rgba(249, 115, 22, 0.6);
}

.dark.module-people::-webkit-scrollbar-thumb {
  background-color: rgba(249, 115, 22, 0.3);
}

.dark.module-people::-webkit-scrollbar-thumb:hover {
  background-color: rgba(249, 115, 22, 0.5);
}

/* Body - Módulo People - com maior especificidade */
html body.module-people::-webkit-scrollbar-thumb {
  background-color: rgba(249, 115, 22, 0.4) !important;
}

html body.module-people::-webkit-scrollbar-thumb:hover {
  background-color: rgba(249, 115, 22, 0.6) !important;
}

html.dark body.module-people::-webkit-scrollbar-thumb {
  background-color: rgba(249, 115, 22, 0.3) !important;
}

html.dark body.module-people::-webkit-scrollbar-thumb:hover {
  background-color: rgba(249, 115, 22, 0.5) !important;
}

/* Módulo Scheduler (roxo) */
.module-scheduler::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.4); /* violet-600 com opacidade */
}

.module-scheduler::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.6);
}

.dark.module-scheduler::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.3);
}

.dark.module-scheduler::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.5);
}

/* Body - Módulo Scheduler - com maior especificidade */
html body.module-scheduler::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.4) !important;
}

html body.module-scheduler::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.6) !important;
}

html.dark body.module-scheduler::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.3) !important;
}

html.dark body.module-scheduler::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.5) !important;
}

/* Módulo Admin (cinza/slate) */
.module-admin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.4); /* slate-500 com opacidade */
}

.module-admin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.6);
}

.dark.module-admin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.3);
}

.dark.module-admin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.5);
}

/* Body - Módulo Admin - com maior especificidade */
html body.module-admin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.4) !important;
}

html body.module-admin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.6) !important;
}

html.dark body.module-admin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.3) !important;
}

html.dark body.module-admin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.5) !important;
}

/* Módulo Financial (verde) */
.module-financial::-webkit-scrollbar-thumb {
  background-color: rgba(5, 150, 105, 0.4); /* emerald-600 com opacidade */
}

.module-financial::-webkit-scrollbar-thumb:hover {
  background-color: rgba(5, 150, 105, 0.6);
}

.dark.module-financial::-webkit-scrollbar-thumb {
  background-color: rgba(5, 150, 105, 0.3);
}

.dark.module-financial::-webkit-scrollbar-thumb:hover {
  background-color: rgba(5, 150, 105, 0.5);
}

/* Body - Módulo Financial - com maior especificidade */
html body.module-financial::-webkit-scrollbar-thumb {
  background-color: rgba(5, 150, 105, 0.4) !important;
}

html body.module-financial::-webkit-scrollbar-thumb:hover {
  background-color: rgba(5, 150, 105, 0.6) !important;
}

html.dark body.module-financial::-webkit-scrollbar-thumb {
  background-color: rgba(5, 150, 105, 0.3) !important;
}

html.dark body.module-financial::-webkit-scrollbar-thumb:hover {
  background-color: rgba(5, 150, 105, 0.5) !important;
}

/* Estilo para modais */
.modal-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.modal-scrollbar::-webkit-scrollbar-thumb {
  border-width: 1px;
}

/* Aplicar scrollbar personalizado a elementos específicos */
.custom-scrollbar {
  scrollbar-width: thin; /* Para Firefox */
  scrollbar-color: rgba(156, 163, 175, 0.5) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

/* Aplicar scrollbar personalizado a elementos específicos por módulo */
.custom-scrollbar.module-people {
  scrollbar-color: rgba(249, 115, 22, 0.4) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

.custom-scrollbar.module-scheduler {
  scrollbar-color: rgba(147, 51, 234, 0.4) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

.custom-scrollbar.module-admin {
  scrollbar-color: rgba(100, 116, 139, 0.4) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

.custom-scrollbar.module-financial {
  scrollbar-color: rgba(5, 150, 105, 0.4) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

/* Dark mode para Firefox */
.dark .custom-scrollbar {
  scrollbar-color: rgba(75, 85, 99, 0.5) rgba(255, 255, 255, 0.05);
}

.dark .custom-scrollbar.module-people {
  scrollbar-color: rgba(249, 115, 22, 0.3) rgba(255, 255, 255, 0.05);
}

.dark .custom-scrollbar.module-scheduler {
  scrollbar-color: rgba(147, 51, 234, 0.3) rgba(255, 255, 255, 0.05);
}

.dark .custom-scrollbar.module-admin {
  scrollbar-color: rgba(100, 116, 139, 0.3) rgba(255, 255, 255, 0.05);
}

.dark .custom-scrollbar.module-financial {
  scrollbar-color: rgba(5, 150, 105, 0.3) rgba(255, 255, 255, 0.05);
}

/*!************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/modules/scheduler/calendar/styles/vibrant-calendar.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Estilos ultra modernos para o react-big-calendar - Versão Vibrante */

/* Estilos para tooltip detalhado de eventos */
.event-tooltip-content {
  text-align: left;
  min-width: 200px;
  max-width: 300px;
}

.tooltip-section {
  margin-bottom: 6px;
  line-height: 1.3;
}

.tooltip-section:last-child {
  margin-bottom: 0;
}

.tooltip-section strong {
  color: #a78bfa;
  font-weight: 600;
}

/* Estilos para eventos na grade semanal */
.event-title {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 2px;
}

.event-details {
  line-height: 1.1;
}

.event-details > div {
  margin-bottom: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-details > div:last-child {
  margin-bottom: 0;
}

/* Estilos para eventos que se estendem por múltiplas horas */
.event-multi-hour {
  border-radius: 8px !important;
  margin-bottom: 0 !important;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 100%;
  position: absolute;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-top: 2px solid #a78bfa;
}

/* Indicador de duração para eventos multi-hora */
.event-duration-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.2);
  color: inherit;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: bold;
  backdrop-filter: blur(4px);
}

/* Garantir que eventos multi-hora tenham altura adequada */
.event-multi-hour .event-title {
  margin-bottom: 4px;
}

.event-multi-hour .event-details {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Efeito visual para eventos que se estendem */
.event-multi-hour::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: currentColor;
  opacity: 0.3;
  border-radius: 0 8px 8px 0;
}

/* Garantir truncamento correto dos eventos */
.calendar-event-text {
  display: block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* Garantir que o container do evento também respeite o truncamento */
.calendar-event-container {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Tornar textos do calendário não selecionáveis */
.modern-calendar-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.modern-calendar-container * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.modern-calendar-container {
  height: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 50%, #f8fafc 100%);
  border-radius: 24px;
  box-shadow: 
    0 20px 40px -10px rgba(107, 33, 168, 0.05),
    0 0 0 1px rgba(192, 132, 252, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  overflow: hidden;
  border: 1px solid rgba(192, 132, 252, 0.2);
  position: relative;
}

/* Dark mode override */
.dark .modern-calendar-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%) !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
  box-shadow: 
    0 20px 40px -10px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(139, 92, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

.modern-calendar-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(139, 92, 246, 0.3) 50%, transparent 100%);
  z-index: 1;
}

.rbc-allday-cell {
  display: none;
}

/* Toolbar customizada mais suave */
.modern-calendar-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28px 36px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
  border-bottom: 1px solid rgba(192, 132, 252, 0.15);
  backdrop-filter: blur(20px);
  position: relative;
  z-index: 2;
}

/* Dark mode override */
.dark .modern-calendar-toolbar {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
  border-bottom: 1px solid rgba(139, 92, 246, 0.2) !important;
}

.modern-calendar-toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(139, 92, 246, 0.3) 50%, transparent 100%);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 28px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

/* Botão Hoje - mais suave */
.today-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 16px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 8px 20px -4px rgba(139, 92, 246, 0.3),
    0 0 0 1px rgba(139, 92, 246, 0.2);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.today-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.today-btn:hover::before {
  left: 100%;
}

.today-btn:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 12px 28px -6px rgba(139, 92, 246, 0.4),
    0 0 0 1px rgba(139, 92, 246, 0.3);
  background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
}

.today-btn:active {
  transform: translateY(0);
}

/* Botões de navegação */
.navigation-buttons {
  display: flex;
  gap: 10px;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(192, 132, 252, 0.3);
  border-radius: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  color: #6b21a8;
  position: relative;
  overflow: hidden;
}

/* Dark mode override */
.dark .nav-btn {
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.8) 0%, rgba(71, 85, 105, 0.6) 100%) !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
  color: #a78bfa !important;
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(167, 139, 250, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s;
}

.nav-btn:hover::before {
  opacity: 1;
}

.nav-btn:hover {
  background: linear-gradient(135deg, rgba(167, 139, 250, 0.2) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-color: rgba(167, 139, 250, 0.4);
  transform: translateY(-2px);
  box-shadow: 
    0 12px 28px -6px rgba(167, 139, 250, 0.25),
    0 0 0 1px rgba(167, 139, 250, 0.2);
  color: #ffffff;
}

.nav-btn:active {
  transform: translateY(0);
}

/* Período atual */
.current-period {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.period-title {
  font-size: 28px;
  font-weight: 800;
  color: #6b21a8;
  margin: 0;
  text-shadow: none;
}

.period-date {
  font-size: 15px;
  color: #9333ea;
  margin: 0;
  font-weight: 500;
  text-transform: capitalize;
  letter-spacing: 0.5px;
}

/* Dark mode override */
.dark .period-title {
  color: #ffffff !important;
  background: linear-gradient(135deg, #ffffff 0%, #a78bfa 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.dark .period-date {
  color: #a78bfa !important;
}

/* Botões de visualização */
.view-buttons {
  display: flex;
  gap: 8px;
  background: rgba(255, 255, 255, 0.6);
  padding: 8px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(192, 132, 252, 0.3);
  box-shadow: 0 4px 12px -2px rgba(107, 33, 168, 0.1);
}

.view-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 18px;
  background: transparent;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  font-size: 14px;
  color: #6b21a8;
  position: relative;
  overflow: hidden;
}

/* Dark mode override */
.dark .view-buttons {
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.8) 0%, rgba(71, 85, 105, 0.6) 100%) !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1) !important;
}

.dark .view-btn {
  color: white !important;
}

.view-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(167, 139, 250, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s;
}

.view-btn:hover::before {
  opacity: 1;
}

.view-btn:hover {
  background: rgba(167, 139, 250, 0.15);
  color: #ffffff;
  transform: translateY(-1px);
}

.view-btn.active {
  background: linear-gradient(135deg, #a78bfa 0%, #7c3aed 100%);
  color: white;
  box-shadow: 
    0 8px 20px -4px rgba(167, 139, 250, 0.4),
    0 0 0 1px rgba(167, 139, 250, 0.3);
  transform: translateY(-1px);
}

/* Calendário principal */
.rbc-calendar {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: transparent;
  border: none;
  border-radius: 0;
  box-shadow: none;
  backdrop-filter: none;
}

/* Header do calendário (esconder o padrão) */
.rbc-toolbar {
  display: none !important;
}

/* Grid do calendário */
.rbc-month-view,
.rbc-time-view {
  border: none;
  border-radius: 0;
  overflow: hidden;
  background: transparent;
}

/* Cabeçalho dos dias */
.rbc-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  border-bottom: 2px solid rgba(192, 132, 252, 0.2) !important;
  padding: 20px 16px !important;
  font-weight: 700 !important;
  color: #6b21a8 !important;
  backdrop-filter: blur(10px);
  font-size: 14px !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  position: relative;
}

/* Dark mode override */
.dark .rbc-header {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
  border-bottom: 2px solid rgba(139, 92, 246, 0.2) !important;
  color: #a78bfa !important;
}

.rbc-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(139, 92, 246, 0.3) 50%, transparent 100%);
}

/* Slots de tempo */
.rbc-time-slot {
  border-bottom: 1px solid rgba(192, 132, 252, 0.2);
  background: rgba(255, 255, 255, 0.3);
  position: relative;
  transition: background 0.3s ease;
}

.rbc-time-slot:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Dark mode override */
.dark .rbc-time-slot {
  border-bottom: 1px solid rgba(139, 92, 246, 0.1) !important;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.3) 0%, rgba(51, 65, 85, 0.2) 100%) !important;
}

.dark .rbc-time-slot:hover {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.4) 0%, rgba(51, 65, 85, 0.3) 100%) !important;
}

.rbc-time-slot::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(139, 92, 246, 0.1) 50%, transparent 100%);
}

.rbc-time-header-content {
  border-left: 1px solid rgba(139, 92, 246, 0.2);
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

/* Células do mês */
.rbc-month-row {
  border-bottom: 1px solid rgba(192, 132, 252, 0.2) !important;
  min-height: 140px !important;
  height: auto !important;
  background: rgba(255, 255, 255, 0.3);
  position: relative;
  transition: background 0.3s ease;
}

.rbc-month-row:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Dark mode override */
.dark .rbc-month-row {
  border-bottom: 1px solid rgba(139, 92, 246, 0.1) !important;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.3) 0%, rgba(51, 65, 85, 0.2) 100%) !important;
}

.dark .rbc-month-row:hover {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.4) 0%, rgba(51, 65, 85, 0.3) 100%) !important;
}

.rbc-month-row::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(139, 92, 246, 0.1) 50%, transparent 100%);
}

.rbc-row-content {
  height: auto !important;
  min-height: 120px !important;
  overflow: visible !important;
  padding: 8px !important;
}

.rbc-date-cell {
  padding: 16px !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  font-weight: 700 !important;
  color: #6b21a8 !important;
  border-right: 1px solid rgba(192, 132, 252, 0.2) !important;
  font-size: 16px !important;
  position: relative;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.rbc-date-cell:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  color: #7c3aed !important;
  transform: scale(1.02);
}

/* Dark mode override */
.dark .rbc-date-cell {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
  color: #a78bfa !important;
  border-right: 1px solid rgba(139, 92, 246, 0.2) !important;
}

.dark .rbc-date-cell:hover {
  background: linear-gradient(135deg, #334155 0%, #475569 100%) !important;
  color: #ffffff !important;
}

.rbc-date-cell::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 1px;
  background: linear-gradient(180deg, transparent 0%, rgba(139, 92, 246, 0.2) 50%, transparent 100%);
}

.rbc-off-range-bg {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%) !important;
}

.rbc-off-range {
  color: #64748b !important;
  opacity: 0.6;
}

/* Hoje */
.rbc-today {
  background: linear-gradient(135deg, rgba(167, 139, 250, 0.2) 0%, rgba(139, 92, 246, 0.1) 100%) !important;
  border-radius: 12px !important;
  position: relative;
  overflow: hidden;
  animation: pulseToday 2s infinite;
}

@keyframes pulseToday {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(167, 139, 250, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(167, 139, 250, 0.1);
  }
}

.rbc-today::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(167, 139, 250, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
  border-radius: 12px;
}

/* Eventos - Cores Vibrantes */
.rbc-event {
  border: none !important;
  border-radius: 12px !important;
  box-shadow: 
    0 8px 20px -4px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  border: 2px solid rgba(255, 255, 255, 0.15) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-weight: 700 !important;
  min-height: 32px !important;
  margin: 2px 3px !important;
  font-size: 11px !important;
  padding: 4px 8px !important;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.rbc-event::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s;
}

.rbc-event:hover::before {
  opacity: 1;
}

.rbc-event:hover {
  transform: translateY(-2px) scale(1.02) !important;
  box-shadow: 
    0 12px 28px -6px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2) !important;
}

.rbc-event-content {
  padding: 4px 6px !important;
  font-size: 11px !important;
  line-height: 1.3 !important;
  font-weight: 700 !important;
  position: relative;
  z-index: 1;
}

/* Cores por status - Versão Suave */
.rbc-event.status-confirmed {
  background: linear-gradient(135deg, #34d399 0%, #10b981 100%) !important;
  color: #064e3b !important;
  box-shadow: 
    0 8px 20px -4px rgba(52, 211, 153, 0.4),
    0 0 0 1px rgba(52, 211, 153, 0.3) !important;
  border: 2px solid #6ee7b7 !important;
}

.rbc-event.status-confirmed:hover {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  transform: translateY(-3px) scale(1.03) !important;
}

.rbc-event.status-pending {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
  color: #78350f !important;
  box-shadow: 
    0 8px 20px -4px rgba(251, 191, 36, 0.4),
    0 0 0 1px rgba(251, 191, 36, 0.3) !important;
  border: 2px solid #fcd34d !important;
}

.rbc-event.status-pending:hover {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  transform: translateY(-3px) scale(1.03) !important;
}

.rbc-event.status-cancelled {
  background: linear-gradient(135deg, #f87171 0%, #ef4444 100%) !important;
  color: #7f1d1d !important;
  box-shadow: 
    0 8px 20px -4px rgba(248, 113, 113, 0.4),
    0 0 0 1px rgba(248, 113, 113, 0.3) !important;
  border: 2px solid #fca5a5 !important;
}

.rbc-event.status-cancelled:hover {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  transform: translateY(-3px) scale(1.03) !important;
}

.rbc-event.status-completed {
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%) !important;
  color: #1e3a8a !important;
  box-shadow: 
    0 8px 20px -4px rgba(96, 165, 250, 0.4),
    0 0 0 1px rgba(96, 165, 250, 0.3) !important;
  border: 2px solid #93c5fd !important;
}

.rbc-event.status-completed:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  transform: translateY(-3px) scale(1.03) !important;
}

.rbc-event.status-no_show {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%) !important;
  color: #1f2937 !important;
  box-shadow: 
    0 8px 20px -4px rgba(156, 163, 175, 0.4),
    0 0 0 1px rgba(156, 163, 175, 0.3) !important;
  border: 2px solid #d1d5db !important;
}

.rbc-event.status-no_show:hover {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
  transform: translateY(-3px) scale(1.03) !important;
}

/* Evento especial "+X mais" */
.rbc-event.more-card {
  background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%) !important;
  color: #ffffff !important;
  font-weight: 700 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 
    0 8px 20px -4px rgba(124, 58, 237, 0.5),
    0 0 0 1px rgba(124, 58, 237, 0.3) !important;
  border: 2px solid #a78bfa !important;
  animation: pulseMore 2s infinite;
}

@keyframes pulseMore {
  0%, 100% {
    box-shadow: 
      0 8px 20px -4px rgba(124, 58, 237, 0.5),
      0 0 0 1px rgba(124, 58, 237, 0.3);
  }
  50% {
    box-shadow: 
      0 12px 28px -6px rgba(124, 58, 237, 0.6),
      0 0 0 2px rgba(124, 58, 237, 0.4);
  }
}

.rbc-event.more-card:hover {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
  transform: translateY(-3px) scale(1.05) !important;
}

/* Time Grid (visualização semanal/diária) */
.rbc-time-view {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%) !important;
}

.rbc-time-header {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
  border-bottom: 2px solid rgba(139, 92, 246, 0.2) !important;
}

.rbc-time-content {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%) !important;
}

.rbc-timeslot-group {
  border-bottom: 1px solid rgba(139, 92, 246, 0.1) !important;
  position: relative;
  transition: background 0.3s ease;
}

.rbc-timeslot-group:hover {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.4) 0%, rgba(51, 65, 85, 0.3) 100%) !important;
}

.rbc-timeslot-group::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(139, 92, 246, 0.1) 50%, transparent 100%);
}

.rbc-time-gutter {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
  border-right: 2px solid rgba(139, 92, 246, 0.2) !important;
  color: #a78bfa !important;
  font-weight: 600 !important;
  font-size: 12px !important;
  position: relative;
}

.rbc-time-gutter::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 1px;
  background: linear-gradient(180deg, transparent 0%, rgba(139, 92, 246, 0.2) 50%, transparent 100%);
}

.rbc-time-gutter .rbc-timeslot-group {
  border-bottom: 1px solid rgba(139, 92, 246, 0.1) !important;
}

.rbc-day-slot {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.3) 0%, rgba(51, 65, 85, 0.2) 100%) !important;
  border-right: 1px solid rgba(139, 92, 246, 0.1) !important;
  position: relative;
  transition: background 0.3s ease;
}

.rbc-day-slot:hover {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.4) 0%, rgba(51, 65, 85, 0.3) 100%) !important;
}

.rbc-day-slot::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 1px;
  background: linear-gradient(180deg, transparent 0%, rgba(139, 92, 246, 0.1) 50%, transparent 100%);
}

.rbc-day-slot .rbc-timeslot-group {
  border-bottom: 1px solid rgba(139, 92, 246, 0.05) !important;
}

.rbc-current-time-indicator {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%) !important;
  height: 3px !important;
  border-radius: 2px !important;
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.5) !important;
  animation: pulseCurrentTime 2s infinite;
}

@keyframes pulseCurrentTime {
  0%, 100% {
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.5);
  }
  50% {
    box-shadow: 0 0 12px rgba(239, 68, 68, 0.8);
  }
}

/* Modal ultra moderno */
.modern-calendar-modal {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
  max-width: 95vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0,0,0,0.25);
  background: rgba(30, 32, 48, 0.98);
  border-radius: 18px;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Garantir que overlay cubra toda a tela */
.modal-overlay {
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(16, 16, 32, 0.55);
  z-index: 9998 !important;
}

.modal-content {
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-radius: 24px;
  box-shadow: 
    0 32px 64px -12px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(139, 92, 246, 0.2);
  max-width: 520px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(139, 92, 246, 0.3) 50%, transparent 100%);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28px 36px;
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(51, 65, 85, 0.8) 100%);
  position: relative;
}

.modal-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(139, 92, 246, 0.2) 50%, transparent 100%);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 14px;
  font-size: 22px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(220, 38, 38, 0.1) 100%);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #ef4444;
  font-size: 18px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.modal-close::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s;
}

.modal-close:hover::before {
  opacity: 1;
}

.modal-close:hover {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 38, 0.2) 100%);
  transform: scale(1.1);
  box-shadow: 0 8px 20px -4px rgba(239, 68, 68, 0.3);
}

.modal-body {
  padding: 28px 36px;
  max-height: 60vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

/* Cards de evento no modal */
.event-card {
  background: linear-gradient(135deg, #111827 0%, #1f2937 100%);
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-radius: 20px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 
    0 8px 20px -4px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(139, 92, 246, 0.1);
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

.event-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(167, 139, 250, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s;
}

.event-card:hover::before {
  opacity: 1;
}

.event-card:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 12px 28px -6px rgba(167, 139, 250, 0.4),
    0 0 0 1px rgba(167, 139, 250, 0.3);
  border-color: rgba(167, 139, 250, 0.5);
  background: linear-gradient(135deg, #a78bfa 0%, #7c3aed 100%);
  color: #1a1d2a;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.event-time {
  font-size: 14px;
  font-weight: 600;
  color: #a78bfa;
  background: linear-gradient(135deg, rgba(167, 139, 250, 0.2) 0%, rgba(139, 92, 246, 0.1) 100%);
  padding: 6px 14px;
  border-radius: 10px;
  border: 1px solid rgba(167, 139, 250, 0.3);
}

.event-status {
  font-size: 12px;
  font-weight: 600;
  padding: 6px 14px;
  border-radius: 10px;
  text-transform: capitalize;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-confirmed {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: #ffffff;
  border-color: #4ade80;
}

.status-pending {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
  border-color: #fbbf24;
}

.status-cancelled {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #ffffff;
  border-color: #f87171;
}

.status-completed {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: #ffffff;
  border-color: #60a5fa;
}

.status-no_show {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: #ffffff;
  border-color: #9ca3af;
}

.event-patient {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 6px;
  letter-spacing: 0.5px;
}

.event-provider {
  font-size: 15px;
  color: #d1d5db;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* Scrollbar customizada - mais suave */
.modal-body::-webkit-scrollbar {
  width: 10px;
}

.modal-body::-webkit-scrollbar-track {
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.3) 0%, rgba(71, 85, 105, 0.2) 100%);
  border-radius: 6px;
  border: 1px solid rgba(139, 92, 246, 0.1);
}

.modal-body::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.5) 0%, rgba(167, 139, 250, 0.3) 100%);
  border-radius: 6px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.7) 0%, rgba(167, 139, 250, 0.5) 100%);
}

/* Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .modern-calendar-toolbar {
    flex-direction: column;
    gap: 20px;
    padding: 20px 24px;
  }
  
  .toolbar-left {
    gap: 20px;
  }
  
  .period-title {
    font-size: 24px;
  }
  
  .view-buttons {
    width: 100%;
    justify-content: center;
  }
  
  .view-btn {
    flex: 1;
    justify-content: center;
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .modal-header {
    padding: 20px 24px;
  }
  
  .modal-body {
    padding: 20px 24px;
  }
  
  .event-card {
    padding: 20px;
  }
  
  .rbc-month-row {
    min-height: 120px !important;
  }
  
  .rbc-date-cell {
    padding: 12px !important;
    font-size: 14px !important;
  }
  
  .modern-calendar-modal {
    max-width: 99vw;
    max-height: 98vh;
    border-radius: 10px;
    padding: 0;
  }
}

.calendar-more-btn {
  z-index: 50 !important;
  position: relative;
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.15);
}

.event-more-badge {
  position: absolute;
  top: 6px;
  right: 8px;
  background: #7c3aed;
  color: #fff;
  font-size: 11px;
  font-weight: bold;
  border-radius: 999px;
  padding: 2px 8px;
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.15);
  cursor: pointer;
  z-index: 100;
  transition: background 0.2s;
}

.event-more-badge:hover {
  background: #a78bfa;
}

.event-multi-hour:hover {
  opacity: 1 !important;
  transform: scale(1.025);
  box-shadow: 0 4px 16px 0 rgba(139, 92, 246, 0.25), 0 0 0 2px #a78bfa;
  border: 1.5px solid #a78bfa;
  z-index: 120;
  transition: box-shadow 0.18s, border 0.18s, transform 0.18s;
}

/* Estilos para o botão de exportar */
.export-section {
  display: flex;
  align-items: center;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 4px 12px rgba(124, 58, 237, 0.3),
    0 0 0 1px rgba(124, 58, 237, 0.2);
}

.export-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.export-btn:hover::before {
  left: 100%;
}

.export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 8px 20px rgba(124, 58, 237, 0.4),
    0 0 0 1px rgba(124, 58, 237, 0.3);
  background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
}

.export-btn:active {
  transform: translateY(0);
  box-shadow: 
    0 2px 8px rgba(124, 58, 237, 0.3),
    0 0 0 1px rgba(124, 58, 237, 0.2);
}

.export-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 
    0 2px 8px rgba(124, 58, 237, 0.2),
    0 0 0 1px rgba(124, 58, 237, 0.1);
}

.export-btn:disabled:hover {
  transform: none;
  box-shadow: 
    0 2px 8px rgba(124, 58, 237, 0.2),
    0 0 0 1px rgba(124, 58, 237, 0.1);
}

/* Responsividade para o botão de exportar */
@media (max-width: 768px) {
  .export-section {
    margin-left: 12px;
  }
  
  .export-btn {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .export-btn span {
    display: none;
  }
}
