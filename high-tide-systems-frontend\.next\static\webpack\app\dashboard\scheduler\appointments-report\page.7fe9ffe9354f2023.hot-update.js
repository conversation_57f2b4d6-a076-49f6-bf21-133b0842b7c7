"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/scheduler/appointments-report/page",{

/***/ "(app-pages-browser)/./src/app/modules/scheduler/appointmentsReport/AppointmentsReport.js":
/*!****************************************************************************!*\
  !*** ./src/app/modules/scheduler/appointmentsReport/AppointmentsReport.js ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentsReport: () => (/* binding */ AppointmentsReport),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_tutorial_TutorialManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/tutorial/TutorialManager */ \"(app-pages-browser)/./src/components/tutorial/TutorialManager.js\");\n/* harmony import */ var _components_tutorial_TutorialTriggerButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/tutorial/TutorialTriggerButton */ \"(app-pages-browser)/./src/components/tutorial/TutorialTriggerButton.js\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_isEqual_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,isEqual,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_isEqual_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,isEqual,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isAfter.js\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_isEqual_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,isEqual,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isEqual.js\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isBefore_isEqual_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isBefore,isEqual,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isBefore.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Download,Edit,Eye,Filter,RefreshCw,Search,Trash2,UserCheck,UserX,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Download,Edit,Eye,Filter,RefreshCw,Search,Trash2,UserCheck,UserX,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Download,Edit,Eye,Filter,RefreshCw,Search,Trash2,UserCheck,UserX,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Download,Edit,Eye,Filter,RefreshCw,Search,Trash2,UserCheck,UserX,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Download,Edit,Eye,Filter,RefreshCw,Search,Trash2,UserCheck,UserX,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Download,Edit,Eye,Filter,RefreshCw,Search,Trash2,UserCheck,UserX,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Download,Edit,Eye,Filter,RefreshCw,Search,Trash2,UserCheck,UserX,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,Download,Edit,Eye,Filter,RefreshCw,Search,Trash2,UserCheck,UserX,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/modules/scheduler/services/appointmentService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/appointmentService.js\");\n/* harmony import */ var _components_calendar_AppointmentModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/calendar/AppointmentModal */ \"(app-pages-browser)/./src/components/calendar/AppointmentModal.js\");\n/* harmony import */ var _components_appointmentsReport_AppointmentTable__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/appointmentsReport/AppointmentTable */ \"(app-pages-browser)/./src/components/appointmentsReport/AppointmentTable.js\");\n/* harmony import */ var _components_appointmentsReport_ReportFilter__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/appointmentsReport/ReportFilter */ \"(app-pages-browser)/./src/components/appointmentsReport/ReportFilter.js\");\n/* harmony import */ var _components_appointmentsReport_ClientReportFilter__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/appointmentsReport/ClientReportFilter */ \"(app-pages-browser)/./src/components/appointmentsReport/ClientReportFilter.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* __next_internal_client_entry_do_not_use__ AppointmentsReport,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Tutorial steps para a página de relatório de agendamentos\nconst getReportTutorialSteps = (isClientUser)=>[\n        {\n            title: isClientUser ? \"Meus Agendamentos\" : \"Relatório\",\n            content: isClientUser ? \"Esta tela permite visualizar seus agendamentos e de pessoas relacionadas a você.\" : \"Esta tela permite visualizar e gerenciar todos os agendamentos do sistema.\",\n            selector: \"h1\",\n            position: \"bottom\"\n        },\n        {\n            title: \"Filtros de Agendamentos\",\n            content: isClientUser ? \"Use estes filtros para encontrar agendamentos específicos por data, status, paciente, local ou tipo de serviço.\" : \"Use estes filtros para encontrar agendamentos específicos por data, status, profissional, paciente, local ou tipo de serviço.\",\n            selector: \"form\",\n            position: \"bottom\"\n        },\n        {\n            title: \"Estatísticas\",\n            content: \"Visualize estatísticas rápidas sobre os agendamentos filtrados, incluindo totais por status.\",\n            selector: \".grid.grid-cols-1.md\\\\:grid-cols-4\",\n            position: \"top\"\n        },\n        {\n            title: \"Tabela de Agendamentos\",\n            content: isClientUser ? \"Veja seus agendamentos em formato de tabela.\" : \"Veja todos os agendamentos em formato de tabela, com opções para editar, excluir e alterar o status.\",\n            selector: \"table\",\n            position: \"top\"\n        },\n        {\n            title: \"Ações de Agendamento\",\n            content: isClientUser ? \"Visualize detalhes dos seus agendamentos.\" : \"Use estes botões para editar, excluir ou alterar o status de um agendamento.\",\n            selector: \".flex.space-x-2\",\n            position: \"left\"\n        },\n        {\n            title: \"Exportar Dados\",\n            content: \"Exporte os agendamentos filtrados em diferentes formatos para análise externa.\",\n            selector: \".export-button\",\n            position: \"left\"\n        }\n    ];\nconst AppointmentsReport = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    // Estado para armazenar os agendamentos\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredAppointments, setFilteredAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalAppointments, setTotalAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(20);\n    // Estado para filtros\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        startDate: null,\n        endDate: null,\n        status: [],\n        providers: [],\n        persons: [],\n        locations: [],\n        serviceTypes: []\n    });\n    // Estado para controle do modal de agendamento\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAppointment, setSelectedAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para confirmação de exclusão\n    const [showConfirmDelete, setShowConfirmDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [appointmentToDelete, setAppointmentToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para seleção em massa\n    const [selectedIds, setSelectedIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Permissões\n    const { can, isClient } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_13__.usePermissions)();\n    const canEdit = can(\"scheduler.appointments.edit\");\n    const canDelete = can(\"scheduler.appointments.delete\");\n    // Get the appropriate tutorial steps based on user type\n    const reportTutorialSteps = getReportTutorialSteps(isClient());\n    // Toast notifications\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    // Estado para ordenação\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('date');\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('desc');\n    // Função para carregar os agendamentos\n    const loadAppointments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AppointmentsReport.useCallback[loadAppointments]\": async function() {\n            let filtersToUse = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : filters;\n            setIsLoading(true);\n            try {\n                // Para clientes, não enviamos o filtro de providers (profissionais)\n                const clientSafeFilters = {\n                    ...filtersToUse\n                };\n                // Se o usuário for cliente, remover o filtro de providers\n                if (isClient() && clientSafeFilters.providers && clientSafeFilters.providers.length > 0) {\n                    console.log(\"[CLIENT-FILTER] Removendo filtro de providers para cliente\");\n                    delete clientSafeFilters.providers;\n                }\n                // Buscar todos os agendamentos de uma vez (sem paginação no backend)\n                const response = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_8__.appointmentService.getAppointments({\n                    // Não enviamos page e limit para buscar todos os itens\n                    // ou enviamos um limite grande para garantir que todos os itens sejam retornados\n                    limit: 1000,\n                    search: clientSafeFilters.search || undefined,\n                    startDate: clientSafeFilters.startDate ? (0,_barrel_optimize_names_format_isAfter_isBefore_isEqual_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(clientSafeFilters.startDate, \"yyyy-MM-dd\") : undefined,\n                    endDate: clientSafeFilters.endDate ? (0,_barrel_optimize_names_format_isAfter_isBefore_isEqual_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(clientSafeFilters.endDate, \"yyyy-MM-dd\") : undefined,\n                    status: clientSafeFilters.status && clientSafeFilters.status.length > 0 ? clientSafeFilters.status : undefined,\n                    providers: !isClient() && clientSafeFilters.providers && clientSafeFilters.providers.length > 0 ? clientSafeFilters.providers : undefined,\n                    persons: clientSafeFilters.persons && clientSafeFilters.persons.length > 0 ? clientSafeFilters.persons : undefined,\n                    locations: clientSafeFilters.locations && clientSafeFilters.locations.length > 0 ? clientSafeFilters.locations : undefined,\n                    serviceTypes: clientSafeFilters.serviceTypes && clientSafeFilters.serviceTypes.length > 0 ? clientSafeFilters.serviceTypes : undefined,\n                    companies: clientSafeFilters.companies && clientSafeFilters.companies.length > 0 ? clientSafeFilters.companies : undefined\n                });\n                // Armazenar todos os agendamentos\n                const allAppointments = response.appointments || [];\n                setAppointments(allAppointments);\n                // Calcular o total de páginas com base no número de itens e no tamanho da página\n                const total = allAppointments.length;\n                const pages = Math.ceil(total / itemsPerPage);\n                setTotalAppointments(total);\n                setTotalPages(pages || 1);\n                // Não precisamos aplicar ordenação e paginação manualmente\n                // O ModuleTable já faz isso internamente\n                setFilteredAppointments(allAppointments);\n            } catch (error) {\n                console.error(\"Erro ao carregar agendamentos:\", error);\n                toast_error({\n                    title: \"Erro\",\n                    message: \"Não foi possível carregar os agendamentos. Tente novamente.\"\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AppointmentsReport.useCallback[loadAppointments]\"], [\n        toast_error,\n        itemsPerPage\n    ]);\n    // Carregar agendamentos apenas na inicialização\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentsReport.useEffect\": ()=>{\n            loadAppointments();\n        }\n    }[\"AppointmentsReport.useEffect\"], []);\n    // Função para aplicar filtros locais (sem chamar a API)\n    const applyLocalFilters = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AppointmentsReport.useCallback[applyLocalFilters]\": ()=>{\n            if (!appointments.length) return;\n            let filtered = [\n                ...appointments\n            ];\n            // Aplicar filtro de busca\n            if (filters.search) {\n                const searchLower = filters.search.toLowerCase();\n                filtered = filtered.filter({\n                    \"AppointmentsReport.useCallback[applyLocalFilters]\": (appointment)=>appointment.title.toLowerCase().includes(searchLower) || appointment.personfullName.toLowerCase().includes(searchLower) || appointment.providerfullName.toLowerCase().includes(searchLower)\n                }[\"AppointmentsReport.useCallback[applyLocalFilters]\"]);\n            }\n            // Aplicar filtro de data\n            if (filters.startDate) {\n                filtered = filtered.filter({\n                    \"AppointmentsReport.useCallback[applyLocalFilters]\": (appointment)=>(0,_barrel_optimize_names_format_isAfter_isBefore_isEqual_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_16__.isAfter)(new Date(appointment.startDate), new Date(filters.startDate)) || (0,_barrel_optimize_names_format_isAfter_isBefore_isEqual_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_17__.isEqual)(new Date(appointment.startDate), new Date(filters.startDate))\n                }[\"AppointmentsReport.useCallback[applyLocalFilters]\"]);\n            }\n            if (filters.endDate) {\n                filtered = filtered.filter({\n                    \"AppointmentsReport.useCallback[applyLocalFilters]\": (appointment)=>(0,_barrel_optimize_names_format_isAfter_isBefore_isEqual_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_18__.isBefore)(new Date(appointment.startDate), new Date(filters.endDate)) || (0,_barrel_optimize_names_format_isAfter_isBefore_isEqual_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_17__.isEqual)(new Date(appointment.startDate), new Date(filters.endDate))\n                }[\"AppointmentsReport.useCallback[applyLocalFilters]\"]);\n            }\n            // Aplicar filtro de status\n            if (filters.status.length > 0) {\n                filtered = filtered.filter({\n                    \"AppointmentsReport.useCallback[applyLocalFilters]\": (appointment)=>filters.status.includes(appointment.status)\n                }[\"AppointmentsReport.useCallback[applyLocalFilters]\"]);\n            }\n            setFilteredAppointments(filtered);\n        }\n    }[\"AppointmentsReport.useCallback[applyLocalFilters]\"], [\n        appointments,\n        filters\n    ]);\n    // Aplicar filtros locais quando os filtros mudarem\n    // Desabilitado para não filtrar automaticamente - apenas quando apertar buscar\n    // useEffect(() => {\n    //   applyLocalFilters();\n    // }, [applyLocalFilters]);\n    // Função para abrir o modal de edição\n    const handleEditAppointment = (appointment)=>{\n        setSelectedAppointment(appointment);\n        setIsModalOpen(true);\n    };\n    // Função para abrir o modal de confirmação de exclusão\n    const handleDeleteAppointment = (appointment)=>{\n        setAppointmentToDelete(appointment);\n        setShowConfirmDelete(true);\n    };\n    // Função para confirmar a exclusão\n    const confirmDelete = async ()=>{\n        if (!appointmentToDelete) return;\n        setIsLoading(true);\n        try {\n            await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_8__.appointmentService.deleteAppointment(appointmentToDelete.id);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Agendamento excluído com sucesso.\"\n            });\n            await loadAppointments();\n        } catch (error) {\n            console.error(\"Erro ao excluir agendamento:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível excluir o agendamento. Tente novamente.\"\n            });\n        } finally{\n            setIsLoading(false);\n            setShowConfirmDelete(false);\n            setAppointmentToDelete(null);\n        }\n    };\n    // Função para fechar o modal de exclusão\n    const cancelDelete = ()=>{\n        setShowConfirmDelete(false);\n        setAppointmentToDelete(null);\n    };\n    // Função para alterar o status do agendamento\n    const handleStatusChange = async (appointmentId, newStatus)=>{\n        setIsLoading(true);\n        try {\n            await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_8__.appointmentService.updateAppointment(appointmentId, {\n                status: newStatus\n            });\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Status do agendamento atualizado com sucesso.\"\n            });\n            await loadAppointments();\n        } catch (error) {\n            console.error(\"Erro ao atualizar status:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível atualizar o status do agendamento. Tente novamente.\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para exportar relatório\n    const handleExport = async function() {\n        let format = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"xlsx\";\n        setIsExporting(true);\n        try {\n            const success = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_8__.appointmentService.exportAppointments(filters, format);\n            if (success) {\n                toast_success({\n                    title: \"Exportação concluída\",\n                    message: \"Os agendamentos foram exportados no formato \".concat(format.toUpperCase(), \".\")\n                });\n            } else {\n                toast_error({\n                    title: \"Falha na exportação\",\n                    message: \"Não foi possível exportar os agendamentos.\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Erro ao exportar:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Ocorreu um erro ao exportar os agendamentos.\"\n            });\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    // Função para selecionar/desselecionar todos\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedIds(filteredAppointments.map((a)=>a.id));\n        } else {\n            setSelectedIds([]);\n        }\n    };\n    // Função para selecionar/desselecionar individual\n    const handleSelectOne = (id, checked)=>{\n        setSelectedIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Função para exclusão em massa\n    const handleBulkDelete = async ()=>{\n        setShowBulkDeleteConfirm(false);\n        setIsLoading(true);\n        try {\n            for (const id of selectedIds){\n                await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_8__.appointmentService.deleteAppointment(id);\n            }\n            setSelectedIds([]);\n            await loadAppointments();\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Agendamentos excluídos com sucesso.\"\n            });\n        } catch (error) {\n            toast_error({\n                title: \"Erro\",\n                message: \"Erro ao excluir agendamentos.\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Botões de ação para cada agendamento\n    const renderActions = (appointment)=>{\n        // Para clientes, não mostrar ações\n        if (isClient()) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 transition-colors\",\n                    onClick: ()=>handleEditAppointment(appointment),\n                    title: \"Visualizar agendamento\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        size: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                        lineNumber: 374,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                    lineNumber: 369,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                lineNumber: 368,\n                columnNumber: 9\n            }, undefined);\n        }\n        const isCompleted = appointment.status === \"COMPLETED\";\n        const isCancelled = appointment.status === \"CANCELLED\";\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex space-x-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 transition-colors\",\n                    onClick: ()=>handleEditAppointment(appointment),\n                    disabled: !canEdit,\n                    title: canEdit ? \"Editar agendamento\" : \"Sem permissão para editar\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        size: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200 transition-colors\",\n                    onClick: ()=>handleDeleteAppointment(appointment),\n                    disabled: !canDelete,\n                    title: canDelete ? \"Excluir agendamento\" : \"Sem permissão para excluir\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        size: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                        lineNumber: 400,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, undefined),\n                !isCompleted && !isCancelled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200 transition-colors\",\n                    onClick: ()=>handleStatusChange(appointment.id, \"COMPLETED\"),\n                    disabled: !canEdit,\n                    title: \"Marcar como conclu\\xeddo\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        size: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                        lineNumber: 410,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                    lineNumber: 404,\n                    columnNumber: 11\n                }, undefined),\n                !isCancelled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"p-1 text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-200 transition-colors\",\n                    onClick: ()=>handleStatusChange(appointment.id, \"CANCELLED\"),\n                    disabled: !canEdit,\n                    title: \"Cancelar agendamento\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        size: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                        lineNumber: 421,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                    lineNumber: 415,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n            lineNumber: 384,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            isClient() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                lineNumber: 436,\n                                columnNumber: 13\n                            }, undefined),\n                            isClient() ? \"Meus Agendamentos\" : \"Relatório de Agendamentos\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                        lineNumber: 432,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            !isClient() && selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBulkDeleteConfirm(true),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all\",\n                                disabled: isLoading,\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                onExport: handleExport,\n                                isExporting: isExporting,\n                                disabled: isLoading || filteredAppointments.length === 0,\n                                className: \"text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                lineNumber: 431,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                title: \"Filtros\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_Download_Edit_Eye_Filter_RefreshCw_Search_Trash2_UserCheck_UserX_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-scheduler-icon dark:text-module-scheduler-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                    lineNumber: 464,\n                    columnNumber: 15\n                }, void 0),\n                description: isClient() ? \"Visualize seus agendamentos e de pessoas relacionadas. Utilize os filtros abaixo para encontrar agendamentos específicos.\" : \"Visualize, filtre e gerencie todos os agendamentos do sistema. Utilize os filtros abaixo para encontrar agendamentos específicos.\",\n                moduleColor: \"scheduler\",\n                filters: isClient() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_appointmentsReport_ClientReportFilter__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    filters: filters,\n                    setFilters: setFilters,\n                    onSearch: ()=>{\n                        setCurrentPage(1); // Voltar para a primeira página ao pesquisar\n                        loadAppointments(filters);\n                        applyLocalFilters(); // Aplicar filtros locais quando buscar\n                    },\n                    onExport: handleExport,\n                    isLoading: isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                    lineNumber: 471,\n                    columnNumber: 13\n                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_appointmentsReport_ReportFilter__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    filters: filters,\n                    setFilters: setFilters,\n                    onSearch: ()=>{\n                        setCurrentPage(1); // Voltar para a primeira página ao pesquisar\n                        loadAppointments(filters);\n                        applyLocalFilters(); // Aplicar filtros locais quando buscar\n                    },\n                    onExport: handleExport,\n                    isLoading: isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                    lineNumber: 483,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                lineNumber: 462,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 p-4 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                children: \"Total de Agendamentos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                lineNumber: 501,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                children: totalAppointments\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                lineNumber: 504,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                        lineNumber: 500,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 p-4 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                children: \"Agendamentos Conclu\\xeddos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-green-600 dark:text-green-400\",\n                                children: filteredAppointments.filter((a)=>a.status === \"COMPLETED\").length\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                        lineNumber: 509,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 p-4 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                children: \"Agendamentos Pendentes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                children: filteredAppointments.filter((a)=>a.status === \"PENDING\").length\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                lineNumber: 522,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 p-4 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                children: \"Agendamentos Cancelados\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-red-600 dark:text-red-400\",\n                                children: filteredAppointments.filter((a)=>a.status === \"CANCELLED\").length\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                lineNumber: 531,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                        lineNumber: 527,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                lineNumber: 499,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_appointmentsReport_AppointmentTable__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                appointments: filteredAppointments,\n                renderActions: renderActions,\n                isLoading: isLoading,\n                currentPage: currentPage,\n                totalPages: totalPages,\n                itemsPerPage: itemsPerPage,\n                setCurrentPage: setCurrentPage,\n                setItemsPerPage: setItemsPerPage,\n                onRefresh: loadAppointments,\n                selectedIds: isClient() ? [] : selectedIds,\n                onSelectAll: isClient() ? ()=>{} : handleSelectAll,\n                onSelectOne: isClient() ? ()=>{} : handleSelectOne,\n                isClient: isClient(),\n                onItemsPerPageChange: (newItemsPerPage)=>{\n                    setItemsPerPage(newItemsPerPage);\n                    const newPages = Math.ceil(filteredAppointments.length / newItemsPerPage);\n                    setTotalPages(newPages);\n                    if (currentPage > newPages) {\n                        setCurrentPage(1);\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                lineNumber: 538,\n                columnNumber: 7\n            }, undefined),\n            isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_calendar_AppointmentModal__WEBPACK_IMPORTED_MODULE_9__.AppointmentModal, {\n                isOpen: isModalOpen,\n                onClose: ()=>{\n                    setIsModalOpen(false);\n                    setSelectedAppointment(null);\n                },\n                selectedAppointment: selectedAppointment,\n                onAppointmentChange: loadAppointments,\n                canEdit: canEdit,\n                canDelete: canDelete\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                lineNumber: 564,\n                columnNumber: 9\n            }, undefined),\n            showConfirmDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[11000]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-bold mb-4 text-gray-900 dark:text-white\",\n                            children: \"Confirmar Exclus\\xe3o\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                            lineNumber: 581,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-6 text-gray-700 dark:text-gray-300\",\n                            children: [\n                                \"Tem certeza que deseja excluir o agendamento\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: [\n                                        \" \",\n                                        appointmentToDelete === null || appointmentToDelete === void 0 ? void 0 : appointmentToDelete.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                    lineNumber: 586,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                            lineNumber: 584,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDelete,\n                                    className: \"px-4 py-2 text-white bg-red-600 rounded hover:bg-red-700\",\n                                    children: \"Excluir\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                    lineNumber: 596,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                    lineNumber: 580,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                lineNumber: 579,\n                columnNumber: 9\n            }, undefined),\n            showBulkDeleteConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[11000]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-bold mb-4 text-gray-900 dark:text-white\",\n                            children: \"Confirmar Exclus\\xe3o em Massa\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                            lineNumber: 611,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-6 text-gray-700 dark:text-gray-300\",\n                            children: [\n                                \"Tem certeza que deseja excluir \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: selectedIds.length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                    lineNumber: 615,\n                                    columnNumber: 46\n                                }, undefined),\n                                \" agendamentos selecionados? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                            lineNumber: 614,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowBulkDeleteConfirm(false),\n                                    className: \"px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                    lineNumber: 618,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBulkDelete,\n                                    className: \"px-4 py-2 text-white bg-red-600 rounded hover:bg-red-700\",\n                                    children: \"Excluir\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                                    lineNumber: 624,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                            lineNumber: 617,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                    lineNumber: 610,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                lineNumber: 609,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tutorial_TutorialManager__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n                lineNumber: 636,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\appointmentsReport\\\\AppointmentsReport.js\",\n        lineNumber: 429,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AppointmentsReport, \"e+0k9iSiD+T+KNwP79zExklOMZo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_13__.usePermissions,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = AppointmentsReport;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppointmentsReport);\nvar _c;\n$RefreshReg$(_c, \"AppointmentsReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/scheduler/appointmentsReport/AppointmentsReport.js\n"));

/***/ })

});