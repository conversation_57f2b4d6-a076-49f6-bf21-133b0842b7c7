"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/tutorial/ContextualHelpButton.js":
/*!*********************************************************!*\
  !*** ./src/components/tutorial/ContextualHelpButton.js ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HelpCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_TutorialContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/TutorialContext */ \"(app-pages-browser)/./src/contexts/TutorialContext.js\");\n/* harmony import */ var _tutorials_tutorialMapping__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\r\n * Botão de ajuda contextual que mostra tutoriais específicos com base na rota atual\r\n */ const ContextualHelpButton = ()=>{\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { startTutorial, isActive, isTutorialCompleted } = (0,_contexts_TutorialContext__WEBPACK_IMPORTED_MODULE_3__.useTutorial)();\n    const [currentTutorial, setCurrentTutorial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Busca o tutorial apropriado quando a rota muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContextualHelpButton.useEffect\": ()=>{\n            const tutorialData = (0,_tutorials_tutorialMapping__WEBPACK_IMPORTED_MODULE_4__.getTutorialForRoute)(pathname);\n            setCurrentTutorial(tutorialData);\n        }\n    }[\"ContextualHelpButton.useEffect\"], [\n        pathname\n    ]);\n    // Função para iniciar o tutorial contextual\n    const handleStartTutorial = ()=>{\n        if (currentTutorial && currentTutorial.steps && currentTutorial.steps.length > 0) {\n            startTutorial(currentTutorial.steps, currentTutorial.name);\n        } else {\n            // Se não temos um tutorial para esta página, podemos mostrar uma mensagem\n            console.log('Nenhum tutorial disponível para esta página');\n        // Você pode adicionar aqui uma notificação para o usuário\n        }\n    };\n    // Se o tutorial já estiver ativo, não mostramos o botão\n    if (isActive) return null;\n    // Determinar cor do módulo baseado na rota\n    const getModuleColor = ()=>{\n        if (pathname.includes('/scheduler/')) return 'scheduler';\n        if (pathname.includes('/people/')) return 'people';\n        if (pathname.includes('/admin/')) return 'admin';\n        if (pathname.includes('/financial/')) return 'financial';\n        if (pathname.includes('/hr/')) return 'hr';\n        if (pathname.includes('/chat/')) return 'chat';\n        if (pathname.includes('/abaplus/')) return 'abaplus';\n        return 'dashboard';\n    };\n    const moduleColor = getModuleColor();\n    const isCompleted = currentTutorial && isTutorialCompleted(currentTutorial.name);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 right-6 z-50\",\n        children: [\n            isHovering && currentTutorial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-16 right-0 bg-white dark:bg-gray-800 shadow-lg rounded-lg p-3 mb-2 w-48 text-sm text-gray-700 dark:text-gray-300 animate-fade-in\",\n                children: [\n                    \"Clique para ver o tutorial desta p\\xe1gina\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 right-5 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white dark:bg-gray-800\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\tutorial\\\\ContextualHelpButton.js\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\tutorial\\\\ContextualHelpButton.js\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleStartTutorial,\n                onMouseEnter: ()=>setIsHovering(true),\n                onMouseLeave: ()=>setIsHovering(false),\n                className: \"\\n          w-12 h-12 rounded-full flex items-center justify-center shadow-lg\\n          transition-all duration-300 hover:shadow-xl relative\\n          \".concat(currentTutorial ? isCompleted ? 'bg-gray-100 text-gray-500 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600' : \"bg-module-\".concat(moduleColor, \"-bg text-module-\").concat(moduleColor, \"-text hover:bg-module-\").concat(moduleColor, \"-hover dark:bg-module-\").concat(moduleColor, \"-bg-dark dark:text-module-\").concat(moduleColor, \"-text-dark dark:hover:bg-module-\").concat(moduleColor, \"-hover-dark\") : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400', \"\\n        \"),\n                \"aria-label\": \"Mostrar tutorial da p\\xe1gina\",\n                disabled: !currentTutorial,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        size: currentTutorial ? 28 : 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\tutorial\\\\ContextualHelpButton.js\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined),\n                    currentTutorial && !isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 h-3 w-3 rounded-full bg-white border-2 border-primary-500 dark:border-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\tutorial\\\\ContextualHelpButton.js\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\tutorial\\\\ContextualHelpButton.js\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\tutorial\\\\ContextualHelpButton.js\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ContextualHelpButton, \"lSCYWRUZKD+HQBq0+xBqwsPpztM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _contexts_TutorialContext__WEBPACK_IMPORTED_MODULE_3__.useTutorial\n    ];\n});\n_c = ContextualHelpButton;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContextualHelpButton);\nvar _c;\n$RefreshReg$(_c, \"ContextualHelpButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/tutorial/ContextualHelpButton.js\n"));

/***/ })

});